<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

$config = include '../config/config.php';

echo json_encode([
    'success' => true,
    'api_key' => $config['auth']['frontend_api_key']
]);
?>
