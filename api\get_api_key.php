<?php
session_start();
header('Content-Type: application/json');

// CORS headers for admin dashboard
$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Credentials: true');

// Check if session exists and is valid
$authenticated = false;
if (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true) {
    // Load config to check session timeout
    try {
        $config = include '../config/security.php';
        if ($config && isset($config['auth']['session_timeout'])) {
            // Check session timeout
            if (time() - ($_SESSION['login_time'] ?? 0) <= $config['auth']['session_timeout']) {
                // Check IP hasn't changed
                if (($_SESSION['user_ip'] ?? '') === $_SERVER['REMOTE_ADDR']) {
                    $authenticated = true;
                    $_SESSION['login_time'] = time(); // Extend session
                }
            }
        }
    } catch (Exception $e) {
        // Config error, deny access
    }
}

if (!$authenticated) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Load config and return API key
try {
    $config = include '../config/config.php';
    if (!$config || !isset($config['auth']['frontend_api_key'])) {
        throw new Exception('API key not found in config');
    }

    echo json_encode([
        'success' => true,
        'api_key' => $config['auth']['frontend_api_key']
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Configuration error']);
}
?>
