<?php
// Clear any previous output first
if (ob_get_level()) {
    ob_clean();
}

// <PERSON><PERSON>m tra authentication bằng API key
require_once '../includes/auth.php';
requireApiAuth();

header('Content-Type: application/json');

$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Tắt hiển thị lỗi
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear any output buffer
if (ob_get_level()) {
    ob_clean();
}

try {
    $dataFile = '../data/links.json';
    $links = [];

    if (!is_dir('../data')) {
        mkdir('../data', 0755, true);
    }

    if (!file_exists($dataFile)) {
        file_put_contents($dataFile, json_encode([]));
    }

    if (file_exists($dataFile)) {
        $content = file_get_contents($dataFile);
        if ($content !== false) {
            $links = json_decode($content, true) ?? [];
        }
    }

    $activeLinks = array_filter($links, function($link) {
        return ($link['status'] ?? 'active') === 'active';
    });

    usort($activeLinks, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

    // Debug: Log số lượng links
    error_log("Total links: " . count($links) . ", Active links: " . count($activeLinks));

    echo json_encode(array_values($activeLinks));
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
?>