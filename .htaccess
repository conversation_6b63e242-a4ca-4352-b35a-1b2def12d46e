RewriteEngine On

RewriteCond %{REQUEST_URI} ^/links/(admin|api|config|data)/
RewriteRule ^(.*)$ - [L]

php_flag display_errors Off
php_flag display_startup_errors Off
php_flag log_errors On

RedirectMatch 404 /\..*$

Options -Indexes

<Files ".htaccess">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Redirect shortlinks - chỉ áp dụng cho shortcode, không cho admin/api/config/data
RewriteCond %{REQUEST_URI} !^/links/(admin|api|config|data)/
RewriteCond %{REQUEST_URI} ^/links/([^/]+)/?$
RewriteCond %{DOCUMENT_ROOT}/links/%1/index.php -f
RewriteRule ^([^/]+)/?$ /links/$1/index.php [L]

<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    <FilesMatch "\.(php)$">
        Header always set Access-Control-Allow-Methods "GET, POST, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
        Header always set Access-Control-Allow-Credentials "true"
    </FilesMatch>
</IfModule>
