<?php
// <PERSON><PERSON><PERSON> tra authentication bằng API key
require_once '../includes/auth.php';
requireApiAuth();

// Tắt hiển thị lỗi
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear any output buffer
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: application/json');

$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Methods: DELETE, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}
header('Access-Control-Allow-Methods: DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }

$input = json_decode(file_get_contents('php://input'), true);
$shortcode = $input['shortcode'] ?? '';

if (empty($shortcode)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing shortcode']);
    exit;
}

$dataFile = '../data/links.json';
if (!file_exists($dataFile)) {
    http_response_code(404);
    echo json_encode(['error' => 'Link not found']);
    exit;
}

$links = json_decode(file_get_contents($dataFile), true) ?? [];
$found = false;

for ($i = 0; $i < count($links); $i++) {
    if ($links[$i]['shortcode'] === $shortcode && ($links[$i]['status'] ?? 'active') === 'active') {
        $links[$i]['status'] = 'deleted';
        $links[$i]['deleted_at'] = date('Y-m-d H:i:s');
        $found = true;
        break;
    }
}

if (!$found) {
    http_response_code(404);
    echo json_encode(['error' => 'Link not found']);
    exit;
}

file_put_contents($dataFile, json_encode($links, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

$shortcodeDir = "../$shortcode";
if (is_dir($shortcodeDir)) {
    if (file_exists("$shortcodeDir/index.php")) {
        unlink("$shortcodeDir/index.php");
    }
    rmdir($shortcodeDir);
}

$logFile = '../data/admin.log';
$logEntry = date('Y-m-d H:i:s') . " | DELETE | $shortcode | " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Lỗi hệ thống']);
}
?>