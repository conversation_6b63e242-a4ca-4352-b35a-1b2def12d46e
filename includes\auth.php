<?php
function checkApiAuth() {
    try {
        $config = include __DIR__ . '/../config/security.php';
        $apiKey = $config['auth']['api_key'] ?? '';
        
        // Kiểm tra API key từ header
        $providedKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? '';
        
        if (empty($apiKey) || empty($providedKey)) {
            return false;
        }
        
        return hash_equals($apiKey, $providedKey);
    } catch (Exception $e) {
        error_log("Auth check failed: " . $e->getMessage());
        return false;
    }
}

function requireApiAuth() {
    if (!checkApiAuth()) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
}
?>
