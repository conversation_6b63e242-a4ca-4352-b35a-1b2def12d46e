<?php
// <PERSON><PERSON><PERSON> tra authentication bằng API key
require_once '../includes/auth.php';
requireApiAuth();

// Tắt hiển thị lỗi
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear any output buffer
if (ob_get_level()) {
    ob_clean();
}

// Set timezone to Vietnam
date_default_timezone_set('Asia/Ho_Chi_Minh');

header('Content-Type: application/json');

$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $shortcode = trim($input['shortcode'] ?? '');
    $targetUrl = trim($input['target_url'] ?? '');

// Validate input
if (empty($shortcode) || empty($targetUrl)) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing required fields']);
    exit;
}

// Validate shortcode format
if (!preg_match('/^[a-zA-Z0-9_-]+$/', $shortcode)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid shortcode format']);
    exit;
}

// Validate URL
if (!filter_var($targetUrl, FILTER_VALIDATE_URL)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid URL']);
    exit;
}

$config = include '../config/security.php';

$processedUrl = $targetUrl;
if (strpos($targetUrl, 'https://t.me/') === 0) {
    $processedUrl = str_replace('https://t.me/', $config['worker_domain'] . '/', $targetUrl);
}

$dataFile = '../data/links.json';
$links = [];
if (file_exists($dataFile)) {
    $links = json_decode(file_get_contents($dataFile), true) ?? [];
}

foreach ($links as $link) {
    if ($link['shortcode'] === $shortcode && ($link['status'] ?? 'active') === 'active') {
        echo json_encode(['success' => false, 'error' => 'Shortcode đã tồn tại']);
        exit;
    }
}
$newLink = [
    'id' => uniqid(),
    'shortcode' => $shortcode,
    'target_url' => $processedUrl,
    'original_url' => $targetUrl,
    'created_at' => date('Y-m-d H:i:s'),
    'created_by' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
    'clicks' => 0,
    'status' => 'active'
];

$links[] = $newLink;

if (!is_dir('../data')) {
    mkdir('../data', 0755, true);
}
file_put_contents($dataFile, json_encode($links, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

$shortcodeDir = "../$shortcode";
if (!is_dir($shortcodeDir)) {
    mkdir($shortcodeDir, 0755, true);
}
$redirectPhp = <<<PHP
<?php
\$shortcode = '{$shortcode}';
\$targetUrl = '{$processedUrl}';
\$dataFile = '../data/links.json';

\$userAgent = \$_SERVER['HTTP_USER_AGENT'] ?? '';
if (empty(\$userAgent) || strpos(\$userAgent, 'bot') !== false) {
    http_response_code(403);
    exit('Access denied');
}

// Cập nhật thống kê
if (file_exists(\$dataFile)) {
    \$links = json_decode(file_get_contents(\$dataFile), true) ?? [];
    for (\$i = 0; \$i < count(\$links); \$i++) {
        if (\$links[\$i]['shortcode'] === \$shortcode && \$links[\$i]['status'] === 'active') {
            \$links[\$i]['clicks']++;
            \$links[\$i]['last_click'] = date('Y-m-d H:i:s');
            \$links[\$i]['last_click_ip'] = \$_SERVER['REMOTE_ADDR'] ?? 'Unknown';
            break;
        }
    }
    file_put_contents(\$dataFile, json_encode(\$links, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
}

// Ghi log chi tiết với thông tin mở rộng
\$logFile = '../data/clicks.log';

// Lấy IP thực tế (xử lý proxy/cloudflare)
function getRealIP() {
    \$headers = [
        'HTTP_CF_CONNECTING_IP',     // Cloudflare
        'HTTP_X_FORWARDED_FOR',      // Proxy
        'HTTP_X_FORWARDED',          // Proxy
        'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
        'HTTP_FORWARDED_FOR',        // Proxy
        'HTTP_FORWARDED',            // Proxy
        'REMOTE_ADDR'                // Standard
    ];

    foreach (\$headers as \$header) {
        if (!empty(\$_SERVER[\$header])) {
            \$ips = explode(',', \$_SERVER[\$header]);
            \$ip = trim(\$ips[0]);

            // Validate IP (accept both public and private IPs)
            if (filter_var(\$ip, FILTER_VALIDATE_IP)) {
                return \$ip;
            }
        }
    }

    return \$_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

// Phân tích thiết bị và trình duyệt chi tiết hơn
function parseUserAgent(\$ua) {
    \$device = 'Desktop';
    \$browser = 'Other';
    \$os = 'Other';

    // Detect device type
    if (preg_match('/iPad/i', \$ua)) {
        \$device = 'Tablet';
    } elseif (preg_match('/Mobile|Android|iPhone|iPod|BlackBerry|Windows Phone|Opera Mini/i', \$ua)) {
        \$device = 'Mobile';
    } elseif (preg_match('/Tablet/i', \$ua)) {
        \$device = 'Tablet';
    }

    // Detect browser (order matters!)
    if (preg_match('/Edg\//i', \$ua)) {
        \$browser = 'Edge';
    } elseif (preg_match('/Chrome/i', \$ua) && !preg_match('/Chromium/i', \$ua)) {
        \$browser = 'Chrome';
    } elseif (preg_match('/Firefox/i', \$ua)) {
        \$browser = 'Firefox';
    } elseif (preg_match('/Safari/i', \$ua) && !preg_match('/Chrome/i', \$ua)) {
        \$browser = 'Safari';
    } elseif (preg_match('/Opera|OPR/i', \$ua)) {
        \$browser = 'Opera';
    } elseif (preg_match('/MSIE|Trident/i', \$ua)) {
        \$browser = 'Internet Explorer';
    } elseif (preg_match('/Chromium/i', \$ua)) {
        \$browser = 'Chromium';
    }

    // Detect OS
    if (preg_match('/Windows NT 10/i', \$ua)) {
        \$os = 'Windows 10';
    } elseif (preg_match('/Windows NT 6\.3/i', \$ua)) {
        \$os = 'Windows 8.1';
    } elseif (preg_match('/Windows NT 6\.2/i', \$ua)) {
        \$os = 'Windows 8';
    } elseif (preg_match('/Windows NT 6\.1/i', \$ua)) {
        \$os = 'Windows 7';
    } elseif (preg_match('/Windows/i', \$ua)) {
        \$os = 'Windows';
    } elseif (preg_match('/Mac OS X 10[._](\d+)/i', \$ua, \$matches)) {
        \$version = \$matches[1];
        \$os = "macOS 10.\$version";
    } elseif (preg_match('/Mac OS X|macOS/i', \$ua)) {
        \$os = 'macOS';
    } elseif (preg_match('/Android (\d+)/i', \$ua, \$matches)) {
        \$os = 'Android ' . \$matches[1];
    } elseif (preg_match('/Android/i', \$ua)) {
        \$os = 'Android';
    } elseif (preg_match('/iPhone OS (\d+)/i', \$ua, \$matches)) {
        \$os = 'iOS ' . \$matches[1];
    } elseif (preg_match('/iOS|iPhone|iPad/i', \$ua)) {
        \$os = 'iOS';
    } elseif (preg_match('/Ubuntu/i', \$ua)) {
        \$os = 'Ubuntu';
    } elseif (preg_match('/Linux/i', \$ua)) {
        \$os = 'Linux';
    }

    return "\$device|\$browser|\$os";
}

// Lấy thông tin quốc gia từ IP
function getCountryFromIP(\$ip) {
    // Check for local IPs
    if (\$ip === '::1' || \$ip === '127.0.0.1' || strpos(\$ip, '192.168.') === 0 || strpos(\$ip, '10.') === 0) {
        return 'VN|Vietnam';
    }

    // Try simple API first
    try {
        \$context = stream_context_create([
            'http' => [
                'timeout' => 2,
                'user_agent' => 'Mozilla/5.0 (compatible; LinkTracker/1.0)'
            ]
        ]);

        \$response = @file_get_contents("http://ip-api.com/json/\$ip?fields=country,countryCode", false, \$context);

        if (\$response) {
            \$data = json_decode(\$response, true);
            if (\$data && isset(\$data['country']) && isset(\$data['countryCode'])) {
                return \$data['countryCode'] . '|' . \$data['country'];
            }
        }
    } catch (Exception \$e) {
        // Ignore errors
    }

    return 'Unknown|Unknown';
}

\$ip = getRealIP();
\$userAgent = \$_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
\$referrer = \$_SERVER['HTTP_REFERER'] ?? 'Direct';

\$deviceInfo = parseUserAgent(\$userAgent);
\$countryInfo = getCountryFromIP(\$ip);

// Format: Time | Shortcode | IP | Country | Device | Browser | OS | UserAgent | Referrer
\$logEntry = date('Y-m-d H:i:s') . " | \$shortcode | \$ip | \$countryInfo | \$deviceInfo | " . substr(\$userAgent, 0, 100) . " | \$referrer\n";
file_put_contents(\$logFile, \$logEntry, FILE_APPEND | LOCK_EX);

// Redirect an toàn
header('Location: ' . \$targetUrl, true, 302);
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('X-Robots-Tag: noindex, nofollow');
exit();
?>
PHP;

file_put_contents("$shortcodeDir/index.php", $redirectPhp);

$logFile = '../data/admin.log';
$logEntry = date('Y-m-d H:i:s') . " | CREATE | $shortcode | " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

    echo json_encode([
        'success' => true,
        'shortcode' => $shortcode,
        'short_url' => $config['base_domain'] . "/$shortcode",
        'target_url' => $processedUrl,
        'original_url' => $targetUrl
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Lỗi hệ thống']);
}
?>