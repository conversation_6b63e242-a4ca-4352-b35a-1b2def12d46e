<?php
// Clear any previous output first
if (ob_get_level()) {
    ob_clean();
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Tắt hiển thị lỗi để không làm hỏng JSON
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear any output buffer
if (ob_get_level()) {
    ob_clean();
}



// Cấu hình bảo mật
try {
    $config = include '../config/security.php';
    if (!$config || !is_array($config)) {
        throw new Exception('Config file invalid');
    }
} catch (Exception $e) {
    // Clear any previous output
    if (ob_get_level()) {
        ob_clean();
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Lỗi cấu hình hệ thống']);
    exit;
}

// Tạo thư mục data nếu chưa có
if (!is_dir('../data')) {
    mkdir('../data', 0755, true);
}

// Không có function nào ở đây - tránh gọi nhầm

if ($_POST['action'] ?? '' === 'login') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');

    if (empty($username) || empty($password)) {
        echo json_encode(['success' => false, 'error' => 'Vui lòng nhập đầy đủ thông tin']);
        exit;
    }

    $attemptFile = '../data/attempts.txt';
    $lockFile = '../data/locked_until.txt';

    $currentAttempts = 0;
    if (file_exists($attemptFile)) {
        $currentAttempts = (int)file_get_contents($attemptFile);
    }

    // Kiểm tra khóa với thời gian
    if ($currentAttempts >= 3) {
        $lockedUntil = 0;
        if (file_exists($lockFile)) {
            $lockedUntil = (int)file_get_contents($lockFile);
        }

        // Nếu chưa có thời gian khóa, tạo mới (khóa 2 giờ)
        if ($lockedUntil == 0) {
            $lockedUntil = time() + (2 * 60 * 60); // 2 giờ
            file_put_contents($lockFile, $lockedUntil, LOCK_EX);
        }

        // Kiểm tra xem đã hết thời gian khóa chưa
        if (time() < $lockedUntil) {
            $remainingTime = ceil(($lockedUntil - time()) / 60);
            echo json_encode([
                'success' => false,
                'error' => "Tài khoản bị khóa. Còn $remainingTime phút nữa",
                'locked' => true
            ]);
            exit;
        } else {
            // Hết thời gian khóa - reset
            unlink($attemptFile);
            unlink($lockFile);
            $currentAttempts = 0;
        }
    }

    if ($username === $config['auth']['admin_username'] && password_verify($password, $config['auth']['admin_password_hash'])) {
        if (file_exists($attemptFile)) {
            unlink($attemptFile);
        }
        $lockFile = '../data/locked_until.txt';
        if (file_exists($lockFile)) {
            unlink($lockFile);
        }

        $_SESSION['admin_authenticated'] = true;
        $_SESSION['login_time'] = time();
        $_SESSION['user_ip'] = $_SERVER['REMOTE_ADDR'];

        echo json_encode(['success' => true]);
    } else {
        // Thất bại - tăng attempts
        $newAttempts = $currentAttempts + 1;
        file_put_contents($attemptFile, $newAttempts, LOCK_EX);

        if ($newAttempts >= 3) {
            // Tạo file khóa với thời gian
            $lockFile = '../data/locked_until.txt';
            $lockedUntil = time() + (2 * 60 * 60); // 2 giờ
            file_put_contents($lockFile, $lockedUntil, LOCK_EX);

            echo json_encode([
                'success' => false,
                'error' => 'Tài khoản bị khóa 2 giờ do đăng nhập sai 3 lần',
                'locked' => true
            ]);
        } else {
            $remaining = 3 - $newAttempts;
            echo json_encode([
                'success' => false,
                'error' => "Sai thông tin đăng nhập. Bạn còn $remaining lần thử nữa"
            ]);
        }
    }
    exit;
}



// Kiểm tra session
if ($_POST['action'] ?? '' === 'check') {
    $authenticated = false;

    if (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true) {
        // Kiểm tra timeout
        if (time() - ($_SESSION['login_time'] ?? 0) <= $config['auth']['session_timeout']) {
            // Kiểm tra IP không đổi
            if ($_SESSION['user_ip'] === $_SERVER['REMOTE_ADDR']) {
                $authenticated = true;
                $_SESSION['login_time'] = time(); // Gia hạn session
            } else {
                session_destroy(); // IP đổi = logout
            }
        } else {
            session_destroy(); // Timeout
        }
    }

    echo json_encode(['authenticated' => $authenticated]);
    exit;
}

// Logout
if ($_POST['action'] ?? '' === 'logout') {
    $logFile = '../data/admin.log';
    $logEntry = date('Y-m-d H:i:s') . " | LOGOUT | " . $_SERVER['REMOTE_ADDR'] . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    session_destroy();
    echo json_encode(['success' => true]);
    exit;
}

http_response_code(400);
echo json_encode(['error' => 'Invalid action']);
?>