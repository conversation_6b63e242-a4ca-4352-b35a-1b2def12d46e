<?php
function checkSecurity() {
    $config = include 'config.php';

    if (!empty($config['allowed_ips'])) {
        $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
        if (!in_array($clientIP, $config['allowed_ips'])) {
            http_response_code(403);
            exit('Access denied from IP');
        }
    }

    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $cacheFile = __DIR__ . "/../data/rate_limit_{$clientIP}.json";
    
    if (file_exists($cacheFile)) {
        $data = json_decode(file_get_contents($cacheFile), true);
        if (time() - $data['first_request'] < $config['rate_limit']['time_window']) {
            if ($data['count'] >= $config['rate_limit']['max_requests']) {
                http_response_code(429);
                exit('Rate limit exceeded');
            }
            $data['count']++;
        } else {
            $data = ['count' => 1, 'first_request' => time()];
        }
    } else {
        $data = ['count' => 1, 'first_request' => time()];
    }
    
    file_put_contents($cacheFile, json_encode($data));

    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $blockedAgents = ['bot', 'crawler', 'spider', 'scraper'];
    foreach ($blockedAgents as $agent) {
        if (stripos($userAgent, $agent) !== false) {
            http_response_code(403);
            exit('Bot access denied');
        }
    }
}

function checkAdminAuth() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['admin_authenticated']) || $_SESSION['admin_authenticated'] !== true) {
        http_response_code(401);
        exit('Authentication required');
    }

    $config = include 'config.php';

    if (time() - ($_SESSION['login_time'] ?? 0) > $config['auth']['session_timeout']) {
        session_destroy();
        http_response_code(401);
        exit('Session expired');
    }

    if ($_SESSION['user_ip'] !== $_SERVER['REMOTE_ADDR']) {
        session_destroy();
        http_response_code(401);
        exit('IP changed - authentication required');
    }

    $_SESSION['login_time'] = time();
}
?>