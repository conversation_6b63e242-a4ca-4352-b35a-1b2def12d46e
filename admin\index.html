<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - GemsRadar Links</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flag-icons@6.6.6/css/flag-icons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            font-family: 'Poppins', sans-serif;
        }
    </style>
    <style>
        .success { background: #22c55e; color: white; }
        .error { background: #ef4444; color: white; }
        .tab-button.active { border-color: #3b82f6; color: #3b82f6; }
        .tab-content { display: block; }
        .tab-content.hidden { display: none; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Login Screen -->
    <div id="loginScreen" class="bg-gray-100 min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow w-96">
            <h2 class="text-xl font-semibold text-center mb-6 text-gray-800">Đăng nhập</h2>

            <div id="loginError" class="hidden error p-3 rounded mb-4 text-sm"></div>

            <form id="loginForm" class="space-y-4">
                <div>
                    <input type="text" id="adminUsername"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-500"
                           placeholder="Tên đăng nhập" required>
                </div>

                <div>
                    <input type="password" id="adminPassword"
                           class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-gray-500"
                           placeholder="Mật khẩu" required>
                </div>

                <button type="submit" id="loginBtn"
                        class="w-full bg-gray-800 text-white py-2 px-4 rounded hover:bg-gray-900">
                    Đăng nhập
                </button>
            </form>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="hidden bg-gray-50 min-h-screen">
        <div class="container mx-auto px-4 py-4">
            <div class="max-w-7xl mx-auto">


                <!-- Tabs -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
                    <div class="border-b border-gray-200">
                        <nav class="flex justify-between items-center px-4">
                            <div class="flex space-x-6">
                                <button onclick="showTab('stats')" id="tab-stats" class="tab-button active py-3 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
                                    <i class="fas fa-chart-bar mr-1"></i>Thống kê
                                </button>
                                <button onclick="showTab('links')" id="tab-links" class="tab-button py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                                    <i class="fas fa-link mr-1"></i>Links
                                </button>
                            </div>

                            <!-- Stats Overview in Menu -->
                            <div class="flex items-center space-x-3">
                                <div class="bg-blue-100 border border-blue-300 rounded-lg px-3 py-1">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-link text-blue-700 text-xs"></i>
                                        <span class="text-xs text-blue-800 font-medium">Links:</span>
                                        <span class="text-xs font-bold text-blue-900" id="totalLinksCount">0</span>
                                    </div>
                                </div>
                                <div class="bg-green-100 border border-green-300 rounded-lg px-3 py-1">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-mouse-pointer text-green-700 text-xs"></i>
                                        <span class="text-xs text-green-800 font-medium">Clicks:</span>
                                        <span class="text-xs font-bold text-green-900" id="totalClicksCount">0</span>
                                    </div>
                                </div>
                                <div class="bg-purple-100 border border-purple-300 rounded-lg px-3 py-1">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-globe text-purple-700 text-xs"></i>
                                        <span class="text-xs text-purple-800 font-medium">IPs:</span>
                                        <span class="text-xs font-bold text-purple-900" id="uniqueIPsCount">0</span>
                                    </div>
                                </div>
                                <button onclick="logout()" class="text-gray-500 hover:text-red-600 py-3 text-sm ml-4">
                                    <i class="fas fa-sign-out-alt mr-1"></i>Đăng xuất
                                </button>
                            </div>
                        </nav>
                    </div>
                </div>

                <!-- Tab Content -->
                <div id="tab-content">
                    <!-- Stats Tab -->
                    <div id="stats-tab" class="tab-content">
                        <!-- Filters -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-3">
                            <div class="flex flex-wrap items-end gap-2">
                                <!-- Quick Time Filters -->
                                <button id="filter-1h" onclick="setQuickFilter('1h')" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">1h</button>
                                <button id="filter-1d" onclick="setQuickFilter('1d')" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">1d</button>
                                <button id="filter-1w" onclick="setQuickFilter('1w')" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">1w</button>
                                <button id="filter-1m" onclick="setQuickFilter('1m')" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">1m</button>

                                <!-- Separator -->
                                <div class="border-l border-gray-300 h-6 mx-1"></div>

                                <!-- Date Filters -->
                                <div class="min-w-24">
                                    <label class="block text-xs text-gray-600 mb-1">Từ ngày</label>
                                    <input type="date" id="dateFrom" class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                </div>
                                <div class="min-w-24">
                                    <label class="block text-xs text-gray-600 mb-1">Đến ngày</label>
                                    <input type="date" id="dateTo" class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                </div>

                                <!-- Other Filters -->
                                <div class="min-w-20">
                                    <label class="block text-xs text-gray-600 mb-1">Link</label>
                                    <select id="shortcodeFilter" class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                        <option value="">Tất cả link</option>
                                    </select>
                                </div>
                                <div class="min-w-20">
                                    <label class="block text-xs text-gray-600 mb-1">Quốc gia</label>
                                    <select id="countryFilter" class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                        <option value="">Tất cả quốc gia</option>
                                    </select>
                                </div>
                                <div class="min-w-16">
                                    <label class="block text-xs text-gray-600 mb-1">Thiết bị</label>
                                    <select id="deviceFilter" class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                        <option value="">Tất cả thiết bị</option>
                                    </select>
                                </div>

                                <!-- Action Buttons -->
                                <button onclick="applyFilters()" class="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700">
                                    <i class="fas fa-filter"></i>
                                </button>
                                <button onclick="clearFilters()" class="bg-gray-500 text-white px-3 py-1 rounded text-xs hover:bg-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Chart -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
                            <div class="p-3 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-sm font-semibold text-gray-800" id="chartTitle">Biểu đồ thống kê</h3>
                                    <div class="flex items-center space-x-2">
                                        <!-- Chart Type Selector Only -->
                                        <select id="chartType" onchange="updateChart()" class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                            <option value="line">Line</option>
                                            <option value="bar">Cột</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="p-3">
                                <canvas id="mainChart" height="200"></canvas>
                            </div>
                        </div>

                        <!-- Analytics Grid - 6 columns -->
                        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-4">
                            <!-- Top Links -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">Top Links</h3>
                                </div>
                                <div id="topLinksContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- Top links load here -->
                                </div>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">Quốc gia</h3>
                                </div>
                                <div id="countriesContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- Countries stats load here -->
                                </div>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">
                                        <i class="fas fa-mobile-alt mr-1 text-gray-600"></i>Thiết bị
                                    </h3>
                                </div>
                                <div id="devicesContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- Devices stats load here -->
                                </div>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">
                                        <i class="fab fa-chrome mr-1 text-gray-600"></i>Trình duyệt
                                    </h3>
                                </div>
                                <div id="browsersContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- Browsers stats load here -->
                                </div>
                            </div>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">
                                        <i class="fas fa-desktop mr-1 text-gray-600"></i>OS
                                    </h3>
                                </div>
                                <div id="osContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- OS stats load here -->
                                </div>
                            </div>

                            <!-- Referrers -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-3 border-b border-gray-200">
                                    <h3 class="text-sm font-semibold text-gray-800">Nguồn truy cập</h3>
                                </div>
                                <div id="referrersContainer" class="p-3 max-h-48 overflow-y-auto">
                                    <!-- Referrers stats load here -->
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Analytics -->
                        <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">Chi tiết truy cập</h3>
                                    <div class="flex items-center space-x-2">
                                        <label class="text-sm text-gray-600">Hiển thị:</label>
                                        <select id="limitSelect" onchange="changeLimit()" class="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500">
                                            <option value="10">10</option>
                                            <option value="20" selected>20</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="detailedStatsContainer" class="p-4">
                                <!-- Detailed stats load here -->
                            </div>
                            <div id="paginationContainer" class="px-4 pb-4">
                                <!-- Pagination load here -->
                            </div>
                        </div>
                    </div>

                    <!-- Links Tab -->
                    <div id="links-tab" class="tab-content hidden">
                        <!-- Thông báo -->
                        <div id="notification" class="hidden p-3 rounded-lg mb-4"></div>

                        <!-- Form tạo link -->
                        <div class="bg-white rounded-lg shadow-sm p-4 mb-4 border border-gray-200">
                            <form id="createLinkForm" class="flex gap-3 items-end">
                                <div class="min-w-24">
                                    <label class="block text-xs text-gray-600 mb-1">Shortcode</label>
                                    <input type="text" id="shortcode"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
                                           placeholder="botcopy" required>
                                </div>
                                <div class="flex-1 min-w-48">
                                    <label class="block text-xs text-gray-600 mb-1">URL đích</label>
                                    <input type="url" id="targetUrl"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:border-blue-500"
                                           placeholder="https://t.me/..." required>
                                </div>
                                <button type="submit"
                                        class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700 whitespace-nowrap">
                                    <i class="fas fa-plus mr-1"></i>Tạo
                                </button>
                            </form>
                        </div>

                        <!-- Danh sách links -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h2 class="text-lg font-semibold text-gray-800">Danh sách Links (<span id="totalLinks">0</span>)</h2>
                                <button onclick="refreshLinks()"
                                        class="bg-gray-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-gray-600 transition-colors">
                                    Làm mới
                                </button>
                            </div>

                            <div id="linksContainer">
                                <!-- Links load here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '../api/';
        const AUTH_URL = 'auth.php';
        let API_KEY = null; // Sẽ được load từ server

        // Auto-detect environment
        const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const BASE_URL = isLocal ?
            `${window.location.protocol}//${window.location.host}/` :
            'https://gemsradar.trade/links/';

        // Kiểm tra authentication khi load trang
        window.addEventListener('load', checkAuth);

        // Kiểm tra auth từ localStorage (không cần server)
        function checkAuth() {
            const isLoggedIn = localStorage.getItem('admin_logged_in');
            const loginTime = localStorage.getItem('admin_login_time');

            // Kiểm tra session timeout (30 phút)
            if (isLoggedIn && loginTime) {
                const now = Date.now();
                const loginTimestamp = parseInt(loginTime);
                const sessionTimeout = 30 * 60 * 1000; // 30 phút

                if (now - loginTimestamp < sessionTimeout) {
                    showMainApp();
                    return;
                }
            }

            // Nếu không có session hoặc hết hạn
            localStorage.removeItem('admin_logged_in');
            localStorage.removeItem('admin_login_time');
            showLoginScreen();
        }

        // Xử lý đăng nhập
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginError = document.getElementById('loginError');
            
            loginBtn.innerHTML = 'Đang đăng nhập...';
            loginBtn.disabled = true;
            loginError.classList.add('hidden');
            
            try {
                const response = await fetch(AUTH_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    credentials: 'same-origin',
                    body: `action=login&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });

                const text = await response.text();
                console.log('Login response text:', text); // Debug

                const result = JSON.parse(text);
                
                if (result.success) {
                    // Lưu trạng thái login vào localStorage
                    localStorage.setItem('admin_logged_in', 'true');
                    localStorage.setItem('admin_login_time', Date.now().toString());
                    showMainApp();
                } else {
                    let errorMessage = result.error || 'Đăng nhập thất bại';

                    if (result.locked) {
                        loginError.style.background = '#dc2626'; // Màu đỏ đậm cho khóa tài khoản
                        document.getElementById('loginBtn').disabled = true;
                        document.getElementById('loginBtn').innerHTML = 'Tài khoản bị khóa';

                        // Nếu có thời gian còn lại, hiển thị đếm ngược
                        if (result.remaining_time) {
                            startCountdown(result.remaining_time);
                        }
                    } else {
                        loginError.style.background = '#ef4444'; // Màu đỏ bình thường
                        document.getElementById('adminPassword').value = '';
                        if (result.remaining_attempts <= 1) {
                            document.getElementById('adminUsername').value = '';
                        }
                    }

                    loginError.textContent = errorMessage;
                    loginError.classList.remove('hidden');
                }
            } catch (error) {
                loginError.textContent = 'Lỗi kết nối: ' + error.message;
                loginError.classList.remove('hidden');
            } finally {
                loginBtn.innerHTML = 'Đăng nhập';
                loginBtn.disabled = false;
            }
        });

        // Đăng xuất
        function logout() {
            // Xóa localStorage
            localStorage.removeItem('admin_logged_in');
            localStorage.removeItem('admin_login_time');

            // Reset API key
            API_KEY = null;

            showLoginScreen();
        }

        // Load API key từ server
        async function loadApiKey() {
            try {
                const response = await fetch(API_BASE + 'get_api_key.php', {
                    credentials: 'same-origin',
                    headers: {
                        'Cache-Control': 'no-cache'
                    }
                });

                console.log('API key response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('API key response:', result);
                    if (result.success && result.api_key) {
                        API_KEY = result.api_key;
                        console.log('API key loaded successfully');
                        return true;
                    }
                } else if (response.status === 401) {
                    console.log('Session expired, need to re-login');
                    // Session expired, force re-login
                    logout();
                    return false;
                }

                console.error('Failed to load API key:', response.status);
                return false;
            } catch (error) {
                console.error('Error loading API key:', error);
                return false;
            }
        }

        // Hiển thị màn hình chính
        async function showMainApp() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');

            // Load API key trước khi làm gì khác
            const apiKeyLoaded = await loadApiKey();
            if (!apiKeyLoaded) {
                // Fallback: use hardcoded API key for testing
                console.log('Trying fallback API key...');
                API_KEY = 'gems_api_f8c3de3d775a6f3ffa32c0b0025e8d665';
                console.log('Using fallback API key');
                // Don't return here, continue with the app
            }

            // Load stats for the default stats tab
            await loadStats();

            // Then set default filter to 1d
            setQuickFilter('1d');
        }

        // Hiển thị màn hình đăng nhập
        function showLoginScreen() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            document.getElementById('adminUsername').value = '';
            document.getElementById('adminPassword').value = '';
        }

        // Hiển thị thông báo
        function showNotification(message, type = 'success') {
            const notification = document.getElementById('notification');
            notification.className = `p-4 rounded-lg mb-6 ${type}`;
            notification.textContent = message;
            notification.classList.remove('hidden');
            
            setTimeout(() => {
                notification.classList.add('hidden');
            }, 5000);
        }

        // Tạo link mới
        document.getElementById('createLinkForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const shortcode = document.getElementById('shortcode').value.trim();
            const targetUrl = document.getElementById('targetUrl').value.trim();
            
            if (!shortcode || !targetUrl) {
                showNotification('Vui lòng điền đầy đủ thông tin', 'error');
                return;
            }
            
            if (!/^[a-zA-Z0-9_-]+$/.test(shortcode)) {
                showNotification('Shortcode chỉ được chứa chữ cái, số, gạch dưới và gạch ngang', 'error');
                return;
            }
            
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = 'Đang tạo...';
            submitButton.disabled = true;
            
            // Kiểm tra API key
            if (!API_KEY) {
                showNotification('API key chưa được tải. Vui lòng đăng nhập lại.', 'error');
                return;
            }

            try {
                const response = await fetch(API_BASE + 'create.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': API_KEY
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        shortcode: shortcode,
                        target_url: targetUrl
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showNotification(`Link tạo thành công: ${result.short_url}`, 'success');
                    document.getElementById('createLinkForm').reset();
                    loadLinks();
                } else {
                    showNotification(result.error || 'Có lỗi xảy ra khi tạo link', 'error');
                }
            } catch (error) {
                showNotification('Lỗi khi tạo link', 'error');
            } finally {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });

        // Load danh sách links
        async function loadLinks() {
            const container = document.getElementById('linksContainer');
            container.innerHTML = '<div class="p-6 text-center text-gray-500">Đang tải...</div>';

            // Kiểm tra API key
            if (!API_KEY) {
                container.innerHTML = '<div class="p-4 text-center text-red-500">API key chưa được tải</div>';
                return;
            }

            try {
                const response = await fetch(API_BASE + 'list.php?t=' + Date.now(), {
                    headers: { 'X-API-Key': API_KEY },
                    credentials: 'same-origin'
                });
                const links = await response.json();
                
                document.getElementById('totalLinks').textContent = links.length;
                
                if (links.length === 0) {
                    container.innerHTML = '<div class="p-4 text-center text-gray-500">Chưa có link nào</div>';
                    return;
                }

                container.innerHTML = links.map(link => `
                    <div class="p-2 border-b border-gray-100 hover:bg-gray-50">
                        <div class="flex justify-between items-center">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center space-x-2">
                                    <a href="${BASE_URL}${link.shortcode}" target="_blank"
                                       class="text-blue-600 hover:underline text-sm font-medium">${BASE_URL}${link.shortcode}</a>
                                    <span class="text-xs text-gray-500">→</span>
                                    <span class="text-xs text-gray-600 truncate">${link.target_url}</span>
                                    <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded ml-2">
                                        ${link.clicks || 0}
                                    </span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-1 ml-3">
                                <button onclick="copyToClipboard('${BASE_URL}${link.shortcode}')"
                                        class="text-gray-400 hover:text-gray-600 text-xs px-2 py-1">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button onclick="deleteLink('${link.shortcode}')"
                                        class="text-red-500 hover:text-red-600 text-xs px-2 py-1">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
                
            } catch (error) {
                container.innerHTML = '<div class="p-4 text-center text-red-500">Lỗi khi tải dữ liệu</div>';
            }
        }

        // Xóa link
        async function deleteLink(shortcode) {
            if (!confirm(`Bạn có chắc chắn muốn xóa link "${shortcode}"?`)) return;

            // Kiểm tra API key
            if (!API_KEY) {
                showNotification('API key chưa được tải. Vui lòng đăng nhập lại.', 'error');
                return;
            }

            try {
                const response = await fetch(API_BASE + 'delete.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': API_KEY
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({ shortcode: shortcode })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showNotification(`Link "${shortcode}" đã được xóa`, 'success');
                    // Force reload với delay
                    setTimeout(() => {
                        loadLinks();
                    }, 500);
                } else {
                    showNotification(result.error || 'Có lỗi xảy ra khi xóa link', 'error');
                }
            } catch (error) {
                showNotification('Lỗi khi xóa link', 'error');
            }
        }

        // Copy link
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('Đã copy link vào clipboard', 'success');
            }).catch(() => {
                showNotification('Không thể copy link', 'error');
            });
        }

        // Refresh links
        function refreshLinks() {
            loadLinks();
        }

        // Đếm ngược thời gian khóa
        function startCountdown(remainingSeconds) {
            const loginBtn = document.getElementById('loginBtn');
            const loginError = document.getElementById('loginError');

            const countdownInterval = setInterval(() => {
                remainingSeconds--;

                if (remainingSeconds <= 0) {
                    clearInterval(countdownInterval);
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = 'Đăng nhập';
                    loginError.classList.add('hidden');
                    return;
                }

                const hours = Math.floor(remainingSeconds / 3600);
                const minutes = Math.floor((remainingSeconds % 3600) / 60);
                const seconds = remainingSeconds % 60;

                loginBtn.innerHTML = `Khóa ${hours}h ${minutes}m ${seconds}s`;
                loginError.textContent = `Tài khoản bị khóa. Còn ${hours}h ${minutes}m ${seconds}s`;
            }, 1000);
        }



        // Global variables for charts and pagination
        let mainChart = null;
        let currentPage = 1;
        let currentLimit = 20;
        let currentFilters = {};
        let currentChartPeriod = 'day'; // Default chart period

        // Load statistics
        async function loadStats() {
            try {
                // Kiểm tra API key
                if (!API_KEY) {
                    console.error('API key not loaded');
                    return;
                }

                // Load links data for stats
                const response = await fetch(API_BASE + 'list.php', {
                    headers: { 'X-API-Key': API_KEY },
                    credentials: 'same-origin'
                });
                const links = await response.json();

                // Calculate basic stats
                const totalLinks = links.length;
                const totalClicks = links.reduce((sum, link) => sum + (link.clicks || 0), 0);

                console.log('loadLinks - totalLinks:', totalLinks, 'totalClicks:', totalClicks);

                // Update overview cards
                document.getElementById('totalLinksCount').textContent = totalLinks;
                document.getElementById('totalClicksCount').textContent = totalClicks;

                // Load top links for analytics
                await loadTopLinks(links);


            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Apply filters
        async function applyFilters() {
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            const shortcode = document.getElementById('shortcodeFilter').value;
            const country = document.getElementById('countryFilter').value;
            const device = document.getElementById('deviceFilter').value;

            currentFilters = {
                date_from: dateFrom,
                date_to: dateTo,
                shortcode: shortcode,
                country: country,
                device: device
                // Remove time_filter when using manual filters
            };

            currentPage = 1; // Reset to first page
            await loadDetailedStats();
        }

        // Change limit
        function changeLimit() {
            currentLimit = parseInt(document.getElementById('limitSelect').value);
            currentPage = 1; // Reset to first page
            loadDetailedStats();
        }

        // Change page
        function changePage(page) {
            currentPage = page;
            loadDetailedStats();
        }

        // Get Vietnam time
        function getVietnamTime() {
            return new Date(new Date().toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
        }

        // Set quick filter
        function setQuickFilter(period) {
            const now = getVietnamTime();
            let dateFrom, dateTo;

            // Clear all quick filter highlights
            ['1h', '1d', '1w', '1m'].forEach(p => {
                const btn = document.getElementById(`filter-${p}`);
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('border-gray-300', 'hover:bg-gray-50');
            });

            // Highlight active filter
            const activeBtn = document.getElementById(`filter-${period}`);
            activeBtn.classList.add('bg-blue-600', 'text-white');
            activeBtn.classList.remove('border-gray-300', 'hover:bg-gray-50');

            // Update chart period and title based on quick filter
            let chartTitle = '';
            switch(period) {
                case '1h':
                    // Last 1 hour - for date filter, use today but will need special handling in backend
                    dateFrom = now.toISOString().split('T')[0];
                    dateTo = now.toISOString().split('T')[0];
                    currentFilters.time_filter = '1h';
                    currentChartPeriod = 'minute';
                    chartTitle = 'Biểu đồ 1 giờ qua (theo phút)';
                    break;
                case '1d':
                    // Today only
                    dateFrom = now.toISOString().split('T')[0];
                    dateTo = now.toISOString().split('T')[0];
                    currentFilters.time_filter = '1d';
                    currentChartPeriod = 'hour'; // Show hourly for today
                    chartTitle = 'Biểu đồ hôm nay (theo giờ)';
                    break;
                case '1w':
                    // Last 7 days
                    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    dateFrom = oneWeekAgo.toISOString().split('T')[0];
                    dateTo = now.toISOString().split('T')[0];
                    currentFilters.time_filter = '1w';
                    currentChartPeriod = 'day';
                    chartTitle = 'Biểu đồ 7 ngày qua';
                    break;
                case '1m':
                    // Last 30 days
                    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    dateFrom = oneMonthAgo.toISOString().split('T')[0];
                    dateTo = now.toISOString().split('T')[0];
                    currentFilters.time_filter = '1m';
                    currentChartPeriod = 'day';
                    chartTitle = 'Biểu đồ 30 ngày qua';
                    break;
            }

            // Update chart title
            document.getElementById('chartTitle').textContent = chartTitle;

            document.getElementById('dateFrom').value = dateFrom;
            document.getElementById('dateTo').value = dateTo;

            // Clear other filters
            document.getElementById('shortcodeFilter').value = '';
            document.getElementById('countryFilter').value = '';
            document.getElementById('deviceFilter').value = '';

            // Update current filters
            currentFilters.date_from = dateFrom;
            currentFilters.date_to = dateTo;
            currentFilters.shortcode = '';
            currentFilters.country = '';
            currentFilters.device = '';

            // Reset page and apply
            currentPage = 1;
            loadDetailedStats();
        }

        // Clear all filters
        function clearFilters() {
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('shortcodeFilter').value = '';
            document.getElementById('countryFilter').value = '';
            document.getElementById('deviceFilter').value = '';

            // Clear quick filter highlights
            ['1h', '1d', '1w', '1m'].forEach(p => {
                const btn = document.getElementById(`filter-${p}`);
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('border-gray-300', 'hover:bg-gray-50');
            });

            // Reset filters and chart
            currentFilters = {};
            currentChartPeriod = 'day';
            currentPage = 1;
            document.getElementById('chartTitle').textContent = 'Biểu đồ thống kê';
            loadDetailedStats();
        }

        // Set default date range (last 30 days)
        function setDefaultDateRange() {
            const today = getVietnamTime();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
        }

        // Load detailed statistics from clicks.log
        async function loadDetailedStats() {
            try {
                // Kiểm tra API key
                if (!API_KEY) {
                    console.error('API key not loaded');
                    return;
                }

                const params = new URLSearchParams();

                // Add filters
                Object.keys(currentFilters).forEach(key => {
                    if (currentFilters[key]) {
                        params.append(key, currentFilters[key]);
                    }
                });

                // Add chart period based on current filter
                params.append('chart_period', currentChartPeriod);

                // Add pagination
                params.append('page', currentPage);
                params.append('limit', currentLimit);

                // Add cache buster to ensure fresh data
                params.append('t', Date.now());

                const url = `../api/stats.php?${params.toString()}`;
                const response = await fetch(url, {
                    headers: { 'X-API-Key': API_KEY },
                    credentials: 'same-origin'
                });
                const stats = await response.json();

                console.log('loadDetailedStats - stats:', stats);
                console.log('currentFilters:', currentFilters);

                document.getElementById('uniqueIPsCount').textContent = stats.uniqueIPs || 0;

                // Don't update total clicks count, always keep the original total from loadStats()
                // The filtered clicks will be shown in the detailed table and chart
                // if (stats.filteredClicks !== undefined) {
                //     console.log('Updating totalClicksCount to:', stats.filteredClicks);
                //     document.getElementById('totalClicksCount').textContent = stats.filteredClicks;
                // }

                // Update filter options
                updateFilterOptions(stats.filterOptions || {});

                // Display analytics charts
                displayCountriesStats(stats.countries || {});
                displayDevicesStats(stats.devices || {});
                displayBrowsersStats(stats.browsers || {});
                displayOSStats(stats.operatingSystems || {});
                displayReferrersStats(stats.referrers || {});

                // Display chart
                displayMainChart(stats.chartData || {});

                // Display detailed stats table
                displayDetailedStats(stats.detailedStats || []);

                // Display pagination
                displayPagination(stats.pagination || {});

            } catch (error) {
                console.error('Error loading detailed stats:', error);
                document.getElementById('uniqueIPsCount').textContent = '0';
            }
        }

        // Update filter options
        function updateFilterOptions(options) {
            const shortcodeSelect = document.getElementById('shortcodeFilter');
            const countrySelect = document.getElementById('countryFilter');
            const deviceSelect = document.getElementById('deviceFilter');

            // Clear existing options with descriptive labels
            shortcodeSelect.innerHTML = '<option value="">Tất cả link</option>';
            countrySelect.innerHTML = '<option value="">Tất cả quốc gia</option>';
            deviceSelect.innerHTML = '<option value="">Tất cả thiết bị</option>';

            // Add new options
            (options.shortcodes || []).forEach(shortcode => {
                shortcodeSelect.innerHTML += `<option value="${shortcode}">${shortcode}</option>`;
            });

            (options.countries || []).forEach(country => {
                countrySelect.innerHTML += `<option value="${country}">${getCountryFlag(country).replace('<span class="fi fi-', '').replace(' mr-2"></span>', '')} ${country}</option>`;
            });

            (options.devices || []).forEach(device => {
                deviceSelect.innerHTML += `<option value="${device}">${device}</option>`;
            });
        }

        // Get country flag
        function getCountryFlag(countryName) {
            const countryFlags = {
                'Vietnam': 'vn',
                'United States': 'us',
                'China': 'cn',
                'Japan': 'jp',
                'South Korea': 'kr',
                'Thailand': 'th',
                'Singapore': 'sg',
                'Malaysia': 'my',
                'Indonesia': 'id',
                'Philippines': 'ph',
                'India': 'in',
                'Australia': 'au',
                'United Kingdom': 'gb',
                'Germany': 'de',
                'France': 'fr',
                'Canada': 'ca',
                'Brazil': 'br',
                'Russia': 'ru',
                'Unknown': null,
                'Local': 'vn'
            };

            const code = countryFlags[countryName];
            return code ? `<span class="fi fi-${code} mr-2"></span>` : '🌍 ';
        }

        // Display countries statistics
        function displayCountriesStats(countries) {
            const container = document.getElementById('countriesContainer');
            const entries = Object.entries(countries).slice(0, 5);

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-xs text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            container.innerHTML = entries.map(([country, count]) => `
                <div class="flex justify-between items-center py-1">
                    <div class="flex items-center">
                        ${getCountryFlag(country)}
                        <span class="text-xs text-gray-700 truncate">${country}</span>
                    </div>
                    <span class="text-xs font-medium text-blue-600">${count}</span>
                </div>
            `).join('');
        }

        // Display devices statistics
        function displayDevicesStats(devices) {
            const container = document.getElementById('devicesContainer');
            const entries = Object.entries(devices);

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-xs text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            const total = entries.reduce((sum, [, count]) => sum + count, 0);
            const colors = ['text-blue-600', 'text-green-600', 'text-yellow-600'];

            container.innerHTML = entries.map(([device, count], index) => {
                const percentage = ((count / total) * 100).toFixed(0);
                const color = colors[index % colors.length];
                return `
                    <div class="flex justify-between items-center py-1">
                        <span class="text-xs text-gray-700">${device}</span>
                        <span class="text-xs font-medium ${color}">${count} (${percentage}%)</span>
                    </div>
                `;
            }).join('');
        }

        // Display browsers statistics
        function displayBrowsersStats(browsers) {
            const container = document.getElementById('browsersContainer');
            const entries = Object.entries(browsers).slice(0, 5);

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-xs text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            container.innerHTML = entries.map(([browser, count]) => `
                <div class="flex justify-between items-center py-1">
                    <span class="text-xs text-gray-700 truncate">${browser}</span>
                    <span class="text-xs font-medium text-green-600">${count}</span>
                </div>
            `).join('');
        }

        // Display OS statistics
        function displayOSStats(operatingSystems) {
            const container = document.getElementById('osContainer');
            const entries = Object.entries(operatingSystems).slice(0, 5);

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-xs text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            container.innerHTML = entries.map(([os, count]) => `
                <div class="flex justify-between items-center py-1">
                    <span class="text-xs text-gray-700 truncate">${os}</span>
                    <span class="text-xs font-medium text-purple-600">${count}</span>
                </div>
            `).join('');
        }

        // Display referrers statistics
        function displayReferrersStats(referrers) {
            const container = document.getElementById('referrersContainer');
            const entries = Object.entries(referrers).slice(0, 5);

            if (entries.length === 0) {
                container.innerHTML = '<p class="text-xs text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            container.innerHTML = entries.map(([referrer, count]) => {
                // Get icon for referrer
                let icon = '🌐';
                if (referrer === 'Direct') {
                    icon = '🔗';
                } else if (referrer.includes('google')) {
                    icon = '🔍';
                } else if (referrer.includes('facebook')) {
                    icon = '📘';
                } else if (referrer.includes('twitter') || referrer.includes('x.com')) {
                    icon = '🐦';
                } else if (referrer.includes('telegram')) {
                    icon = '✈️';
                } else if (referrer.includes('youtube')) {
                    icon = '📺';
                }

                return `
                    <div class="flex justify-between items-center py-1">
                        <div class="flex items-center space-x-1">
                            <span class="text-xs">${icon}</span>
                            <span class="text-xs text-gray-700 truncate">${referrer}</span>
                        </div>
                        <span class="text-xs font-medium text-indigo-600">${count}</span>
                    </div>
                `;
            }).join('');
        }

        // Display detailed stats table
        function displayDetailedStats(stats) {
            const container = document.getElementById('detailedStatsContainer');

            if (stats.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">Chưa có dữ liệu thống kê</p>';
                return;
            }

            container.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full text-xs">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Thời gian</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Link</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">IP</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Quốc gia</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Thiết bị</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Browser</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">OS</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500">Nguồn</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            ${stats.map(stat => {
                                // Process referrer for display
                                let referrerDisplay = 'Direct';
                                if (stat.referrer && stat.referrer !== 'Direct') {
                                    try {
                                        const url = new URL(stat.referrer);
                                        referrerDisplay = url.hostname.replace('www.', '');
                                    } catch {
                                        referrerDisplay = stat.referrer.substring(0, 20) + '...';
                                    }
                                }

                                return `
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">${stat.time.substring(5, 16)}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs font-medium text-blue-600">${stat.shortcode}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">${stat.ip}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">
                                            <div class="flex items-center">
                                                ${getCountryFlag(stat.country || 'Unknown')}
                                                <span>${stat.country || 'Unknown'}</span>
                                            </div>
                                        </td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">${stat.device || 'Unknown'}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">${stat.browser || 'Unknown'}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900">${stat.os || 'Unknown'}</td>
                                        <td class="px-2 py-2 whitespace-nowrap text-xs text-gray-900" title="${stat.referrer || 'Direct'}">${referrerDisplay}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // Display pagination
        function displayPagination(pagination) {
            const container = document.getElementById('paginationContainer');

            if (!pagination || pagination.total_pages <= 1) {
                container.innerHTML = '';
                return;
            }

            const { current_page, total_pages, total_records, limit, has_prev, has_next } = pagination;

            let paginationHTML = `
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Hiển thị ${((current_page - 1) * limit) + 1} - ${Math.min(current_page * limit, total_records)} của ${total_records} bản ghi
                    </div>
                    <div class="flex items-center space-x-1">
            `;

            // Previous button
            if (has_prev) {
                paginationHTML += `<button onclick="changePage(${current_page - 1})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">‹ Trước</button>`;
            }

            // Page numbers
            const startPage = Math.max(1, current_page - 2);
            const endPage = Math.min(total_pages, current_page + 2);

            if (startPage > 1) {
                paginationHTML += `<button onclick="changePage(1)" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">1</button>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="px-2 text-sm text-gray-500">...</span>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === current_page;
                paginationHTML += `<button onclick="changePage(${i})" class="px-3 py-1 text-sm border ${isActive ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 hover:bg-gray-50'} rounded">${i}</button>`;
            }

            if (endPage < total_pages) {
                if (endPage < total_pages - 1) {
                    paginationHTML += `<span class="px-2 text-sm text-gray-500">...</span>`;
                }
                paginationHTML += `<button onclick="changePage(${total_pages})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">${total_pages}</button>`;
            }

            // Next button
            if (has_next) {
                paginationHTML += `<button onclick="changePage(${current_page + 1})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">Sau ›</button>`;
            }

            paginationHTML += `
                    </div>
                </div>
            `;

            container.innerHTML = paginationHTML;
        }

        // Load top links
        function loadTopLinks(links) {
            console.log('loadTopLinks - links:', links);
            const container = document.getElementById('topLinksContainer');
            const sortedLinks = links.sort((a, b) => (b.clicks || 0) - (a.clicks || 0)).slice(0, 5);

            console.log('loadTopLinks - sortedLinks:', sortedLinks);

            if (sortedLinks.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center">Chưa có dữ liệu</p>';
                return;
            }

            container.innerHTML = sortedLinks.map((link, index) => `
                <div class="flex items-center justify-between py-1 ${index < sortedLinks.length - 1 ? 'border-b border-gray-100' : ''}">
                    <span class="text-xs font-medium text-gray-900 truncate">${link.shortcode}</span>
                    <span class="text-xs font-semibold text-blue-600">${link.clicks || 0}</span>
                </div>
            `).join('');
        }



        // Display main chart
        function displayMainChart(chartData) {
            console.log('Chart data received:', chartData);

            const ctx = document.getElementById('mainChart').getContext('2d');

            if (mainChart) {
                mainChart.destroy();
            }

            const labels = Object.keys(chartData);
            const data = Object.values(chartData);
            const chartType = document.getElementById('chartType')?.value || 'line';

            console.log('Labels:', labels);
            console.log('Data:', data);

            // If no data, show empty chart with proper labels
            if (labels.length === 0) {
                const period = document.getElementById('chartPeriod')?.value || 'day';
                const now = new Date();
                const emptyLabels = [];
                const emptyData = [];

                // Generate empty labels based on period
                switch(period) {
                    case 'minute':
                        for (let i = 59; i >= 0; i--) {
                            const time = new Date(now.getTime() - i * 60 * 1000);
                            emptyLabels.push(time.getHours().toString().padStart(2, '0') + ':' + time.getMinutes().toString().padStart(2, '0'));
                            emptyData.push(0);
                        }
                        break;
                    case 'hour':
                        for (let i = 23; i >= 0; i--) {
                            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                            emptyLabels.push(time.getHours().toString().padStart(2, '0') + ':00');
                            emptyData.push(0);
                        }
                        break;
                    case 'day':
                        for (let i = 6; i >= 0; i--) {
                            const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
                            emptyLabels.push((time.getDate()).toString().padStart(2, '0') + '/' + (time.getMonth() + 1).toString().padStart(2, '0'));
                            emptyData.push(0);
                        }
                        break;
                    default:
                        emptyLabels.push('No data');
                        emptyData.push(0);
                }

                labels.length = 0;
                data.length = 0;
                labels.push(...emptyLabels);
                data.push(...emptyData);
            }

            const config = {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Clicks',
                        data: data,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: chartType === 'line' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.8)',
                        tension: chartType === 'line' ? 0.1 : 0,
                        fill: chartType === 'line',
                        borderWidth: chartType === 'bar' ? 1 : 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            };

            mainChart = new Chart(ctx, config);
        }

        // Update chart when type changes (period is controlled by quick filters)
        function updateChart() {
            loadDetailedStats();
        }

        // Initialize when tab is shown
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
                btn.classList.remove('border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.remove('hidden');

            // Add active class to selected button
            const activeBtn = document.getElementById('tab-' + tabName);
            activeBtn.classList.add('active');
            activeBtn.classList.remove('border-transparent', 'text-gray-500');
            activeBtn.classList.add('border-blue-500', 'text-blue-600');

            // Load data for each tab
            if (tabName === 'stats') {
                setDefaultDateRange();
                loadStats();
            } else if (tabName === 'links') {
                loadLinks();
            }
        }

        // Auto-check auth every 5 minutes
        setInterval(checkAuth, 300000);
    </script>
</body>
</html>