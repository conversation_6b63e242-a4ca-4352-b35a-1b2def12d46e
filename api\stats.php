<?php
// Clear any previous output first
if (ob_get_level()) {
    ob_clean();
}

// Kiểm tra authentication bằng API key
require_once '../includes/auth.php';
requireApiAuth();

header('Content-Type: application/json');

$isLocal = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
if ($isLocal) {
    header('Access-Control-Allow-Origin: *');
} else {
    $allowedOrigins = [
        'https://gemsradar.trade',
        'https://gemsradar.trade/links',
        'https://gemsradar.trade/links/admin'
    ];
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'https://gemsradar.trade') === 0) {
        header('Access-Control-Allow-Origin: ' . $origin);
    } else {
        header('Access-Control-Allow-Origin: https://gemsradar.trade');
    }
}
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Tắt hiển thị lỗi
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear any output buffer
if (ob_get_level()) {
    ob_clean();
}

// Set timezone to Vietnam
date_default_timezone_set('Asia/Ho_Chi_Minh');

try {
    $clicksFile = '../data/clicks.log';

    // Get filter parameters
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $shortcodeFilter = $_GET['shortcode'] ?? '';
    $countryFilter = $_GET['country'] ?? '';
    $deviceFilter = $_GET['device'] ?? '';
    $browserFilter = $_GET['browser'] ?? '';
    $timeFilter = $_GET['time_filter'] ?? '';
    $chartPeriod = $_GET['chart_period'] ?? 'day';

    // Override chart period based on time filter for better UX
    if ($timeFilter) {
        switch($timeFilter) {
            case '1h':
                $chartPeriod = 'minute'; // Show by minute for 1 hour
                break;
            case '1d':
                $chartPeriod = 'hour'; // Show hourly for today
                break;
            case '1w':
                $chartPeriod = 'day';
                break;
            case '1m':
                $chartPeriod = 'day';
                break;
        }
    }

    // Pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = max(10, min(100, intval($_GET['limit'] ?? 20))); // 10-100, default 20
    $offset = ($page - 1) * $limit;

    // Calculate time thresholds for quick filters
    // Set timezone to Vietnam
    date_default_timezone_set('Asia/Ho_Chi_Minh');
    $timeThreshold = null;

    switch($timeFilter) {
        case '1h':
            $oneHourAgo = new DateTime('now', new DateTimeZone('Asia/Ho_Chi_Minh'));
            $oneHourAgo->modify('-1 hour');
            $timeThreshold = $oneHourAgo->format('Y-m-d H:i:s');
            error_log("1h filter - timeThreshold: " . $timeThreshold);
            break;
        case '1d':
            $today = new DateTime('now', new DateTimeZone('Asia/Ho_Chi_Minh'));
            $timeThreshold = $today->format('Y-m-d 00:00:00');
            break;
        case '1w':
            $oneWeekAgo = new DateTime('now', new DateTimeZone('Asia/Ho_Chi_Minh'));
            $oneWeekAgo->modify('-7 days');
            $timeThreshold = $oneWeekAgo->format('Y-m-d H:i:s');
            break;
        case '1m':
            $oneMonthAgo = new DateTime('now', new DateTimeZone('Asia/Ho_Chi_Minh'));
            $oneMonthAgo->modify('-30 days');
            $timeThreshold = $oneMonthAgo->format('Y-m-d H:i:s');
            break;
    }

    $stats = [
        'uniqueIPs' => 0,
        'todayClicks' => 0,
        'detailedStats' => [],
        'recentActivity' => [],
        'dailyStats' => [],
        'hourlyStats' => []
    ];

    if (!file_exists($clicksFile)) {
        echo json_encode($stats);
        exit;
    }
    
    $lines = file($clicksFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $uniqueIPs = [];
    $todayClicks = 0;
    $today = date('Y-m-d');
    $detailedStats = [];
    $recentActivity = [];
    
    $countries = [];
    $devices = [];
    $browsers = [];
    $operatingSystems = [];
    $referrers = [];
    $chartStats = [];
    $allDetailedStats = [];
    $filteredCount = 0;

    // Parse log lines with filtering
    foreach (array_reverse($lines) as $line) {
        $parts = explode(' | ', $line);

        // Support both old and new format
        if (count($parts) >= 4) {
            $time = $parts[0];
            $shortcode = $parts[1];
            $ip = $parts[2];

            // New format: Time | Shortcode | IP | Country | Device|Browser|OS | UserAgent | Referrer (7 parts)
            if (count($parts) >= 7 && strpos($parts[3], '|') !== false) {
                // New format detected
                $countryParts = explode('|', $parts[3]);
                $countryCode = $countryParts[0] ?? 'Unknown';
                $countryName = $countryParts[1] ?? 'Unknown';

                $deviceParts = explode('|', $parts[4]);
                $device = $deviceParts[0] ?? 'Unknown';
                $browser = $deviceParts[1] ?? 'Unknown';
                $os = $deviceParts[2] ?? 'Unknown';

                $userAgent = $parts[5] ?? 'Unknown';
                $referrer = $parts[6] ?? 'Direct';
            } else {
                // Old format: Time | Shortcode | IP | UserAgent | Referrer (5 parts)
                $countryCode = 'Unknown';
                $countryName = 'Unknown';
                $device = 'Unknown';
                $browser = 'Unknown';
                $os = 'Unknown';
                $userAgent = $parts[3] ?? 'Unknown';
                $referrer = $parts[4] ?? 'Direct';
            }

            // Apply filters
            $passFilter = true;

            // Time filter (quick filters have priority over date range)
            if ($timeThreshold) {
                if ($time < $timeThreshold) $passFilter = false;
            } else {
                // Regular date range filter
                if ($dateFrom && $time < $dateFrom) $passFilter = false;
                if ($dateTo && $time > $dateTo . ' 23:59:59') $passFilter = false;
            }

            if ($shortcodeFilter && $shortcode !== $shortcodeFilter) $passFilter = false;
            if ($countryFilter && $countryName !== $countryFilter) $passFilter = false;
            if ($deviceFilter && $device !== $deviceFilter) $passFilter = false;
            if ($browserFilter && $browser !== $browserFilter) $passFilter = false;

            if (!$passFilter) continue;

            // Count unique IPs
            if (!in_array($ip, $uniqueIPs)) {
                $uniqueIPs[] = $ip;
            }

            // Count today's clicks
            if (strpos($time, $today) === 0) {
                $todayClicks++;
            }

            // Chart stats based on period - simplified
            $chartKey = '';
            try {
                $dateTime = new DateTime($time);

                switch($chartPeriod) {
                    case 'minute':
                        $chartKey = $dateTime->format('Y-m-d H:i');
                        break;
                    case 'hour':
                        $chartKey = $dateTime->format('Y-m-d H:00');
                        break;
                    case 'day':
                        $chartKey = $dateTime->format('Y-m-d');
                        break;
                    case 'week':
                        $chartKey = $dateTime->format('Y-W');
                        break;
                    case 'month':
                        $chartKey = $dateTime->format('Y-m');
                        break;
                    case 'year':
                        $chartKey = $dateTime->format('Y');
                        break;
                }

                if ($chartKey) {
                    $chartStats[$chartKey] = ($chartStats[$chartKey] ?? 0) + 1;
                }
            } catch (Exception $e) {
                // Skip invalid dates
                continue;
            }

            // Count statistics
            $countries[$countryName] = ($countries[$countryName] ?? 0) + 1;
            $devices[$device] = ($devices[$device] ?? 0) + 1;
            $browsers[$browser] = ($browsers[$browser] ?? 0) + 1;
            $operatingSystems[$os] = ($operatingSystems[$os] ?? 0) + 1;

            // Process referrer
            $referrerDomain = 'Direct';
            if ($referrer !== 'Direct' && !empty($referrer)) {
                $parsedUrl = parse_url($referrer);
                if ($parsedUrl && isset($parsedUrl['host'])) {
                    $referrerDomain = $parsedUrl['host'];
                    // Clean up common prefixes
                    $referrerDomain = preg_replace('/^www\./', '', $referrerDomain);
                }
            }
            $referrers[$referrerDomain] = ($referrers[$referrerDomain] ?? 0) + 1;

            // Add to all detailed stats for counting
            $allDetailedStats[] = [
                'time' => $time,
                'shortcode' => $shortcode,
                'ip' => $ip,
                'country' => $countryName,
                'countryCode' => $countryCode,
                'device' => $device,
                'browser' => $browser,
                'os' => $os,
                'userAgent' => substr($userAgent, 0, 100),
                'referrer' => $referrer === 'Direct' ? 'Direct' : substr($referrer, 0, 50)
            ];

            $filteredCount++;

            // Add to recent activity (limit to 20)
            if (count($recentActivity) < 20) {
                $recentActivity[] = [
                    'time' => $time,
                    'shortcode' => $shortcode,
                    'ip' => $ip,
                    'country' => $countryName,
                    'device' => $device
                ];
            }
        }
    }

    // Sort statistics
    arsort($countries);
    arsort($devices);
    arsort($browsers);
    arsort($operatingSystems);
    arsort($referrers);
    
    // Prepare chart data based on period - simplified and accurate
    $chartData = [];
    $now = new DateTime();

    switch($chartPeriod) {
        case 'minute':
            // Last 60 minutes
            for ($i = 59; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i minutes");
                $key = $time->format('Y-m-d H:i');
                $label = $time->format('H:i');
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;

        case 'hour':
            // Last 24 hours
            for ($i = 23; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i hours");
                $key = $time->format('Y-m-d H:00');
                $label = $time->format('H:i');
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;

        case 'day':
            // Last 7 days (more reasonable for testing)
            for ($i = 6; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i days");
                $key = $time->format('Y-m-d');
                $label = $time->format('d/m');
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;

        case 'week':
            // Last 8 weeks
            for ($i = 7; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i weeks");
                $key = $time->format('Y-W');
                $label = 'W' . $time->format('W');
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;

        case 'month':
            // Last 6 months
            for ($i = 5; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i months");
                $key = $time->format('Y-m');
                $label = $time->format('M');
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;

        case 'year':
            // Last 3 years
            for ($i = 2; $i >= 0; $i--) {
                $time = clone $now;
                $time->modify("-$i years");
                $key = $time->format('Y');
                $label = $key;
                $chartData[$label] = $chartStats[$key] ?? 0;
            }
            break;
    }

    // Apply pagination to detailed stats
    $totalPages = ceil($filteredCount / $limit);
    $detailedStats = array_slice($allDetailedStats, $offset, $limit);

    $stats['uniqueIPs'] = count($uniqueIPs);
    $stats['todayClicks'] = $todayClicks;
    $stats['detailedStats'] = $detailedStats;
    $stats['countries'] = array_slice($countries, 0, 10, true);
    $stats['devices'] = $devices;
    $stats['browsers'] = array_slice($browsers, 0, 10, true);
    $stats['operatingSystems'] = array_slice($operatingSystems, 0, 10, true);
    $stats['referrers'] = array_slice($referrers, 0, 10, true);
    $stats['chartData'] = $chartData;
    $stats['filteredClicks'] = $filteredCount; // Total clicks matching current filter

    // Pagination info
    $stats['pagination'] = [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_records' => $filteredCount,
        'limit' => $limit,
        'has_prev' => $page > 1,
        'has_next' => $page < $totalPages
    ];

    // Get available filter options
    $stats['filterOptions'] = [
        'shortcodes' => array_keys(array_count_values(array_column($detailedStats, 'shortcode'))),
        'countries' => array_keys($countries),
        'devices' => array_keys($devices),
        'browsers' => array_keys($browsers)
    ];

    // Only return recent activity if requested
    if (isset($_GET['recent'])) {
        $stats = ['recentActivity' => $recentActivity];
    }
    
    echo json_encode($stats);
    
} catch (Exception $e) {
    echo json_encode([
        'uniqueIPs' => 0,
        'todayClicks' => 0,
        'detailedStats' => [],
        'recentActivity' => []
    ]);
}
?>
