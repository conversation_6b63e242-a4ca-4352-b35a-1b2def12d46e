<?php
// Auto-detect environment and load appropriate config

function getConfig() {
    $isLocal = (
        $_SERVER['HTTP_HOST'] === 'localhost' ||
        $_SERVER['HTTP_HOST'] === '127.0.0.1' ||
        strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0
    );
    
    if ($isLocal) {
        return include __DIR__ . '/local.php';
    } else {
        return include __DIR__ . '/security.php';
    }
}

// Return config based on environment
return getConfig();
